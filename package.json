{"name": "tom-and-jerry-chase-data", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "ts-node --compiler-options {\\\"module\\\":\\\"CommonJS\\\"} prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@prisma/client": "^6.8.2", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.14", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "postcss": "^8.4.31", "prisma": "^6.8.2", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}